/**
 * Teste para verificar se o problema dos caracteres estranhos na descrição SEO foi resolvido
 * 
 * Usage: node test-seo-description-fix.js
 * Note: Requires OPENAI_API_KEY environment variable to be set
 */

const testCases = [
  {
    name: "Produto Simples",
    productInfo: {
      name: "Smartphone Samsung Galaxy",
      category: "Eletrónicos",
      features: ["Ecrã AMOLED", "Câmara 108MP", "Bateria 5000mAh"],
      keywords: ["smartphone", "samsung", "android"],
      targetAudience: "Utilizadores de tecnologia",
      additionalInfo: "Inclui carregador rápido de 25W e capa protetora transparente."
    }
  },
  {
    name: "Produto com Caracteres Especiais",
    productInfo: {
      name: "Azeite Extra Virgem \"Premium\"",
      category: "Alimentação",
      features: ["100% natural", "Primeira extração", "Acidez < 0.3%"],
      keywords: ["azeite", "extra virgem", "premium"],
      targetAudience: "Amantes de culinária",
      additionalInfo: "Produzido em olivais centenários da região de Trás-os-Montes. Certificação DOP e prémio 'Melhor Azeite 2023'."
    }
  }
];

async function testSeoDescriptionFix() {
  console.log("🧪 TESTE DE CORREÇÃO DA DESCRIÇÃO SEO");
  console.log("=" .repeat(60));
  
  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`\n📝 Teste ${i + 1}: ${testCase.name}`);
    console.log("-".repeat(40));
    
    try {
      const response = await fetch('http://localhost:3000/api/generate-description', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'generate',
          productInfo: testCase.productInfo
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      const seoDescription = data.seoContent.shortDescription;
      
      console.log(`📊 Resultados:`);
      console.log(`   Comprimento: ${seoDescription.length} caracteres`);
      console.log(`   Dentro do limite SEO: ${seoDescription.length <= 160 ? '✅' : '❌'}`);
      console.log(`   Conteúdo: "${seoDescription}"`);
      
      // Verificar caracteres problemáticos
      const hasProblematicChars = /[""'']/g.test(seoDescription);
      const hasControlChars = /[\u0000-\u001F\u007F-\u009F]/g.test(seoDescription);
      
      console.log(`   Caracteres problemáticos: ${hasProblematicChars ? '❌ Encontrados' : '✅ Nenhum'}`);
      console.log(`   Caracteres de controle: ${hasControlChars ? '❌ Encontrados' : '✅ Nenhum'}`);
      
      // Mostrar caracteres especiais se existirem
      if (hasProblematicChars) {
        const problematicMatches = seoDescription.match(/[""'']/g);
        console.log(`   Caracteres encontrados: ${problematicMatches?.join(', ')}`);
      }
      
      if (hasControlChars) {
        const controlMatches = seoDescription.match(/[\u0000-\u001F\u007F-\u009F]/g);
        console.log(`   Caracteres de controle: ${controlMatches?.map(c => `\\u${c.charCodeAt(0).toString(16).padStart(4, '0')}`).join(', ')}`);
      }
      
      // Verificar se a descrição faz sentido
      const hasValidContent = seoDescription.length > 50 && !seoDescription.includes('undefined') && !seoDescription.includes('null');
      console.log(`   Conteúdo válido: ${hasValidContent ? '✅' : '❌'}`);
      
    } catch (error) {
      console.error(`❌ Erro no teste ${testCase.name}:`, error.message);
    }
    
    console.log("-".repeat(40));
    
    // Delay para evitar rate limiting
    if (i < testCases.length - 1) {
      console.log("⏳ Aguardando 2 segundos...\n");
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  console.log("\n✅ Teste concluído!");
  console.log("\n💡 Se ainda houver problemas:");
  console.log("   1. Verifique os logs do servidor para mais detalhes");
  console.log("   2. Confirme que as funções de limpeza estão a funcionar");
  console.log("   3. Teste com diferentes tipos de produtos");
}

// Executar o teste
testSeoDescriptionFix().catch(console.error);
