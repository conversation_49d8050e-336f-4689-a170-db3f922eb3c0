const fetch = require('node-fetch');

// Test products to validate the new concise formatting
const testProducts = [
  {
    name: "Smartphone Premium 5G",
    category: "Tecnologia",
    features: ["Ecrã OLED 6.8\"", "Câmara tripla 108MP", "Bateria 5000mAh"],
    keywords: ["smartphone premium", "telemóvel 5G", "câmara profissional"],
    targetAudience: "Utilizadores exigentes",
    additionalInfo: "Processador Snapdragon 8 Gen 3 (3.2GHz), 12GB RAM LPDDR5, 512GB UFS 4.0. Ecrã Dynamic AMOLED 2X com taxa de atualização 120Hz. Sistema de câmaras com sensor principal Samsung ISOCELL HM3 de 108MP, ultra-wide 12MP e teleobjetiva 10MP com zoom ótico 3x."
  },
  {
    name: "Sapatos de Corrida Profissionais",
    category: "Desporto",
    features: ["Sola com amortecimento reativo", "Material respirável", "Design ergonómico"],
    keywords: ["sapatos corrida", "ténis desporto", "calçado profissional"],
    targetAudience: "Atletas e corredores",
    additionalInfo: "Sola em EVA com tecnologia de retorno de energia, cabedal em mesh técnico com ventilação lateral, palmilha ortopédica removível, sistema de ataque rápido, peso de apenas 280g."
  },
  {
    name: "Suplemento Proteína Whey",
    category: "Nutrição",
    features: ["25g proteína por dose", "Sabor chocolate", "Dissolução rápida"],
    keywords: ["proteína whey", "suplemento fitness", "nutrição desportiva"],
    targetAudience: "Praticantes de fitness",
    additionalInfo: "Proteína isolada de soro de leite com 90% de pureza, aminoácidos essenciais BCAA 5.5g por dose, leucina 2.7g, sem açúcares adicionados, adoçado com stevia natural, certificação ISO 22000."
  }
];

async function testDescriptionGeneration() {
  console.log('🧪 Testando geração de descrições concisas...\n');

  for (let i = 0; i < testProducts.length; i++) {
    const product = testProducts[i];
    console.log(`📦 Produto ${i + 1}: ${product.name}`);
    console.log('─'.repeat(50));

    try {
      const response = await fetch('http://localhost:3000/api/generate-description', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'generate',
          productInfo: product
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      const description = data.seoContent.wooCommerceMainDescription;
      
      // Analyze the description
      const wordCount = description.replace(/<[^>]*>/g, '').split(/\s+/).filter(word => word.length > 0).length;
      const charCount = description.length;
      const hasLists = description.includes('<ul>') && description.includes('<li>');
      const strongCount = (description.match(/<strong>/g) || []).length;
      
      console.log(`📊 Análise da Descrição:`);
      console.log(`   • Palavras: ${wordCount} (objetivo: 150-250)`);
      console.log(`   • Caracteres: ${charCount}`);
      console.log(`   • Usa listas: ${hasLists ? '✅' : '❌'}`);
      console.log(`   • Tags <strong>: ${strongCount}`);
      
      // Check if within target word count
      const isOptimalLength = wordCount >= 150 && wordCount <= 250;
      console.log(`   • Comprimento ideal: ${isOptimalLength ? '✅' : '❌'}`);
      
      console.log(`\n📝 Descrição WooCommerce:`);
      console.log(description);
      
      console.log(`\n📄 Descrição Curta:`);
      console.log(data.seoContent.wooCommerceShortDescription);
      
      console.log(`\n🔍 SEO (${data.seoContent.shortDescription.length} chars):`);
      console.log(data.seoContent.shortDescription);
      
      console.log('\n' + '='.repeat(80) + '\n');
      
      // Wait a bit between requests to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.error(`❌ Erro ao testar produto ${product.name}:`, error.message);
      console.log('\n' + '='.repeat(80) + '\n');
    }
  }
}

// Run the test
testDescriptionGeneration().catch(console.error);
