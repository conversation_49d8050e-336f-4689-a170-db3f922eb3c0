import type { Metadata, Viewport } from "next";
import "./globals.css";
import InstallPWA from "@/components/InstallPWA";
import Navbar from "@/components/navbar";
import { StagewiseToolbar } from '@stagewise/toolbar-next';

// Definindo a variável de fonte para usar fontes do sistema
const fontSans = {
  variable: '--font-sans',
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  themeColor: '#0f172a',
};

export const metadata: Metadata = {
  title: 'Image Optimizer',
  description: 'Otimize suas imagens facilmente com esta ferramenta web',
  manifest: '/manifest.json',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'Image Optimizer'
  },
  icons: {
    icon: '/icons/icon-192x192.png',
    apple: '/icons/apple-icon-180x180.png',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const stagewiseConfig = {
    plugins: []
  };

  return (
    <html lang="pt-BR" suppressHydrationWarning>
      <head>
        <link rel="apple-touch-icon" href="/icons/apple-icon-180x180.png" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        
        {/* Splash Screens para iOS */}
        <link rel="apple-touch-startup-image" media="screen and (device-width: 430px) and (device-height: 932px) and (-webkit-device-pixel-ratio: 3)" href="/splash/splash-1290x2796.png" />
        <link rel="apple-touch-startup-image" media="screen and (device-width: 393px) and (device-height: 852px) and (-webkit-device-pixel-ratio: 3)" href="/splash/splash-1179x2556.png" />
        <link rel="apple-touch-startup-image" media="screen and (device-width: 390px) and (device-height: 844px) and (-webkit-device-pixel-ratio: 3)" href="/splash/splash-1170x2532.png" />
        <link rel="apple-touch-startup-image" media="screen and (device-width: 428px) and (device-height: 926px) and (-webkit-device-pixel-ratio: 3)" href="/splash/splash-1284x2778.png" />
        <link rel="apple-touch-startup-image" media="screen and (device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3)" href="/splash/splash-1125x2436.png" />
        <link rel="apple-touch-startup-image" media="screen and (device-width: 276px) and (device-height: 597px) and (-webkit-device-pixel-ratio: 3)" href="/splash/splash-828x1792.png" />
        <link rel="apple-touch-startup-image" media="screen and (device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3)" href="/splash/splash-1242x2688.png" />
        <link rel="apple-touch-startup-image" media="screen and (device-width: 250px) and (device-height: 445px) and (-webkit-device-pixel-ratio: 3)" href="/splash/splash-750x1334.png" />
        <link rel="apple-touch-startup-image" media="screen and (device-width: 414px) and (device-height: 736px) and (-webkit-device-pixel-ratio: 3)" href="/splash/splash-1242x2208.png" />
        <link rel="apple-touch-startup-image" media="screen and (device-width: 540px) and (device-height: 720px) and (-webkit-device-pixel-ratio: 3)" href="/splash/splash-1620x2160.png" />
        <link rel="apple-touch-startup-image" media="screen and (device-width: 556px) and (device-height: 741px) and (-webkit-device-pixel-ratio: 3)" href="/splash/splash-1668x2224.png" />
        <link rel="apple-touch-startup-image" media="screen and (device-width: 683px) and (device-height: 911px) and (-webkit-device-pixel-ratio: 3)" href="/splash/splash-2048x2732.png" />
      </head>
      <body className={`font-sans ${fontSans.variable} bg-gray-50 dark:bg-gray-900 min-h-screen`} suppressHydrationWarning>
        <Navbar />
        <main className="pt-32 px-4 sm:px-6 lg:px-8">
          <div className="max-w-[1440px] mx-auto space-y-8">
            {children}
          </div>
        </main>
        <InstallPWA />
        {process.env.NODE_ENV === 'development' && (
          <StagewiseToolbar config={stagewiseConfig} />
        )}
      </body>
    </html>
  );
}
