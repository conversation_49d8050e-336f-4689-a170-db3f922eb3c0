"use client";
import React from "react";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { usePathname } from "next/navigation";

export default function Navbar({ className }: { className?: string }) {
  const pathname = usePathname();

  return (
    <div
      className={cn("fixed top-10 inset-x-0 max-w-2xl mx-auto z-50", className)}
    >
      <nav
        className="relative rounded-full border border-transparent dark:bg-black dark:border-white/[0.2] bg-white shadow-input flex justify-center space-x-8 px-8 py-6"
        style={{
          backdropFilter: 'blur(25px) saturate(200%)',
          WebkitBackdropFilter: 'blur(25px) saturate(200%)',
          backgroundColor: 'rgba(120, 117, 117, 0.4)'
        }}
      >
        <Link
          href="/"
          className={cn(
            "text-black dark:text-white hover:opacity-[0.9] transition-opacity duration-300 font-medium",
            pathname === "/" && "opacity-[0.9]"
          )}
        >
          Otimização de Imagens
        </Link>
        <Link
          href="/product-description"
          className={cn(
            "text-black dark:text-white hover:opacity-[0.9] transition-opacity duration-300 font-medium",
            pathname === "/product-description" && "opacity-[0.9]"
          )}
        >
          Descrições de Produtos
        </Link>
      </nav>
    </div>
  );
}
