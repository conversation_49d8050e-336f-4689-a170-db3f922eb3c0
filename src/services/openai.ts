// Este arquivo agora serve apenas como definição de tipos
// A lógica de chamada à API foi movida para src/app/api/generate-description/route.ts

export interface ProductInfo {
  name: string;
  category?: string;
  features?: string[];
  keywords?: string[];
  targetAudience?: string;
  additionalInfo?: string;
}

// Estas funções foram movidas para a API route
// Mantidas aqui apenas para referência de tipos
/* eslint-disable @typescript-eslint/no-unused-vars */
export async function generateProductDescription(_productInfo: ProductInfo): Promise<string> {
  throw new Error('Esta função foi movida para a API route');
}


/* eslint-enable @typescript-eslint/no-unused-vars */ 