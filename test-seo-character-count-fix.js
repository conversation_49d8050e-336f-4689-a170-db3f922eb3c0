/**
 * Test to verify that the SEO description character count issue is fixed
 * 
 * This test ensures that:
 * 1. AI prompts no longer include character count examples that confuse the model
 * 2. Post-processing removes any character counts that might slip through
 * 3. Frontend displays character counts separately from content
 * 4. Copy functionality provides clean content without character counts
 * 
 * Usage: node test-seo-character-count-fix.js
 * Note: Requires OPENAI_API_KEY environment variable to be set
 */

const testProducts = [
  {
    name: "Mistura completa para hamster",
    category: "Alimentação Animal",
    features: ["Trigo", "Sementes variadas", "Vegetais desidratados"],
    keywords: ["hamster", "mistura", "alimentação"],
    targetAudience: "Donos de hamsters",
    additionalInfo: "Nutrição diária equilibrada para máxima vitalidade."
  },
  {
    name: "Smartphone Samsung Galaxy A54",
    category: "Eletrónicos",
    features: ["Ecrã AMOLED 6.4\"", "Câmara 50MP", "Bateria 5000mAh", "5G"],
    keywords: ["smartphone", "samsung", "android"],
    targetAudience: "Utilizadores de tecnologia",
    additionalInfo: "Inclui carregador rápido de 25W e capa protetora transparente."
  },
  {
    name: "Mesa de escritório em madeira maciça",
    category: "Mobiliário",
    features: ["Madeira de carvalho", "120x60cm", "Gavetas organizadoras", "Acabamento natural"],
    keywords: ["mesa", "escritório", "madeira"],
    targetAudience: "Profissionais que trabalham em casa",
    additionalInfo: "Montagem incluída. Garantia de 5 anos contra defeitos de fabrico."
  }
];

async function testSeoCharacterCountFix() {
  console.log("🧪 TESTE DE CORREÇÃO - CONTAGEM DE CARACTERES NA DESCRIÇÃO SEO");
  console.log("=".repeat(80));
  console.log("Este teste verifica se as descrições SEO estão limpas, sem contagens de caracteres.");
  console.log("");

  let allTestsPassed = true;
  const results = [];

  for (let i = 0; i < testProducts.length; i++) {
    const product = testProducts[i];
    console.log(`📝 Teste ${i + 1}/3: ${product.name}`);
    console.log("-".repeat(50));

    try {
      const response = await fetch('http://localhost:3000/api/generate-description', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'generate',
          productInfo: product
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const seoDescription = data.seoContent.shortDescription;
      
      // Validation checks
      const checks = {
        hasContent: seoDescription && seoDescription.length > 0,
        noCaracteresWord: !seoDescription.includes('caracteres'),
        noCharsWord: !seoDescription.includes('chars'),
        noParenthesesWithNumbers: !/\(\s*\d+/.test(seoDescription),
        noBracketsWithNumbers: !/\[\s*\d+/.test(seoDescription),
        lengthValid: seoDescription.length >= 120 && seoDescription.length <= 160,
        isPortuguese: /[àáâãçéêíóôõú]/i.test(seoDescription) || /\b(para|com|de|em|do|da|dos|das)\b/i.test(seoDescription)
      };

      const allChecksPassed = Object.values(checks).every(check => check === true);
      
      console.log(`📊 Resultados da Validação:`);
      console.log(`   ✅ Tem conteúdo: ${checks.hasContent ? 'SIM' : 'NÃO'}`);
      console.log(`   ✅ Sem palavra "caracteres": ${checks.noCaracteresWord ? 'SIM' : 'NÃO'}`);
      console.log(`   ✅ Sem palavra "chars": ${checks.noCharsWord ? 'SIM' : 'NÃO'}`);
      console.log(`   ✅ Sem parênteses com números: ${checks.noParenthesesWithNumbers ? 'SIM' : 'NÃO'}`);
      console.log(`   ✅ Sem colchetes com números: ${checks.noBracketsWithNumbers ? 'SIM' : 'NÃO'}`);
      console.log(`   ✅ Comprimento válido (120-160): ${checks.lengthValid ? 'SIM' : 'NÃO'} (${seoDescription.length} chars)`);
      console.log(`   ✅ Conteúdo em português: ${checks.isPortuguese ? 'SIM' : 'NÃO'}`);
      
      console.log(`\n📄 Descrição SEO Gerada:`);
      console.log(`"${seoDescription}"`);
      
      console.log(`\n🎯 Status: ${allChecksPassed ? '✅ PASSOU' : '❌ FALHOU'}`);
      
      if (!allChecksPassed) {
        allTestsPassed = false;
      }

      results.push({
        product: product.name,
        passed: allChecksPassed,
        length: seoDescription.length,
        description: seoDescription,
        checks
      });

    } catch (error) {
      console.error(`❌ Erro ao testar produto "${product.name}":`, error.message);
      allTestsPassed = false;
      results.push({
        product: product.name,
        passed: false,
        error: error.message
      });
    }

    console.log("\n" + "=".repeat(80) + "\n");
    
    // Wait between requests to avoid rate limiting
    if (i < testProducts.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  // Summary
  console.log("📋 RESUMO DOS TESTES");
  console.log("=".repeat(50));
  
  const passedTests = results.filter(r => r.passed).length;
  const totalTests = results.length;
  
  console.log(`✅ Testes que passaram: ${passedTests}/${totalTests}`);
  console.log(`${allTestsPassed ? '🎉' : '⚠️'} Status geral: ${allTestsPassed ? 'TODOS OS TESTES PASSARAM' : 'ALGUNS TESTES FALHARAM'}`);
  
  if (!allTestsPassed) {
    console.log("\n❌ Testes que falharam:");
    results.filter(r => !r.passed).forEach(result => {
      console.log(`   • ${result.product}: ${result.error || 'Validação falhou'}`);
    });
  }

  console.log("\n🔍 Detalhes das descrições geradas:");
  results.filter(r => r.description).forEach(result => {
    console.log(`   • ${result.product} (${result.length} chars): "${result.description.substring(0, 60)}..."`);
  });

  process.exit(allTestsPassed ? 0 : 1);
}

// Run the test
testSeoCharacterCountFix().catch(error => {
  console.error('❌ Erro fatal no teste:', error);
  process.exit(1);
});
